"use client";

import { useState, useEffect } from "react";
import Head from "next/head";
import Image from "next/image";
import {
  ChevronDown,
  Check,
  X,
  Copy,
  MapPin,
  ExternalLink,
  Box,
  Truck,
  AlertCircle,
  MessageCircleQuestion,
} from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ProductService } from "@/services/product.service";
import {
  Product,
  ExternalProductDetails,
  InternalProductDetails,
} from "@/data/models/product.model";
import { useCartDispatch, addToCart } from "@/lib/CartContext";
import { AddressService } from "@/services/address.service";
import { ExchangeRateService } from "@/services/exchange.service";
import { country_currencies, shipping_rate_countries } from "@/lib/constants";
import { useCurrency } from "@/components/app/CurrencyProvider";
import { packItemsIntoParcels } from "@/lib/shipping";
import { MPLoading } from "@/components/index/mp-loading";
import { Skeleton } from "@/components/ui/skeleton";
import ReactMarkdown from "react-markdown";
import { useAuth } from "@/hooks/use-auth";
import {
  formatCurrencyFallback,
  formatCurrencyWithDigits,
} from "@/lib/currency";

// Default export is the page component
export default function ProductPage({
  params,
}: {
  params: { productId: string };
}) {
  const dispatch = useCartDispatch();
  const [quantity, setQuantity] = useState(1);
  const [selectedImage, setSelectedImage] = useState(0);
  const [discountCopied, setDiscountCopied] = useState(false);
  const [activeAccordion, setActiveAccordion] = useState<number | null>(null);
  const [currentProduct, setCurrentProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  // Currency is provided by CurrencyProvider (useCurrency)
  const { currencyCode, currencySource, exchangeRate, isLoadingExchangeRate } =
    useCurrency();
  const userCurrencyCode = currencyCode;

  const [selectedVariantOptions, setSelectedVariantOptions] = useState<
    Record<string, string>
  >({});
  const [selectedVariant, setSelectedVariant] = useState<any>(null);
  const [userAddress, setUserAddress] = useState<any>(null);
  const [isAlertDialogOpen, setIsAlertDialogOpen] = useState<boolean>(false);

  const addressService = new AddressService();
  const exchangeRateService = new ExchangeRateService();
  const { isAuthenticated, loading: authLoading } = useAuth();

  // Helper function to extract actual value from option_value (handles both string and object formats)
  const getOptionValue = (optionValue: any): string => {
    if (typeof optionValue === "string") {
      return optionValue;
    }
    if (typeof optionValue === "object" && optionValue?.value) {
      return optionValue.value;
    }
    return "";
  };

  // Helper function to find matching variant based on selected options
  const findMatchingVariant = (
    options: Record<string, string>,
    product?: Product,
  ) => {
    const sourceProduct = product || currentProduct;
    if (!sourceProduct?.variants?.variants) return null;

    return sourceProduct.variants.variants.find((variant) => {
      return Object.entries(options).every(([key, value]) => {
        const variantValue = getOptionValue(variant.option_values[key]);
        return variantValue === value;
      });
    });
  };

  // Helper function to get current pricing (from variant or primary_data)
  const getCurrentPricing = () => {
    if (selectedVariant) {
      return {
        price: selectedVariant.price || currentProduct?.primary_data.price || 0,
        sale_price:
          selectedVariant.sale_price || currentProduct?.primary_data.sale_price,
        stock: selectedVariant.stock || 0,
      };
    }
    return {
      price: currentProduct?.primary_data.price || 0,
      sale_price: currentProduct?.primary_data.sale_price,
      stock: (currentProduct?.details as any)?.stock || 0,
    };
  };

  // Helper function to get current images (from variant, shared, and primary) with deduplication
  const getCurrentImages = () => {
    const images: any[] = [];
    const seenNames = new Set<string>();

    // Helper function to add image if not already seen
    const addImageIfUnique = (image: any) => {
      if (image && image.name && !seenNames.has(image.name)) {
        seenNames.add(image.name);
        images.push(image);
      } else if (image && !image.name) {
        // If image doesn't have a name, add it anyway (fallback for legacy images)
        images.push(image);
      }
    };

    // Add variant-specific images (if variant is selected and has images)
    if (selectedVariant?.images && selectedVariant.images.length > 0) {
      selectedVariant.images.forEach(addImageIfUnique);
    }

    // Always add shared images (if available)
    if (
      currentProduct?.shared_images &&
      currentProduct.shared_images.length > 0
    ) {
      currentProduct.shared_images.forEach(addImageIfUnique);
    }

    return images.filter(Boolean);
  };

  // Helper function to convert price using stored exchange rate (render-time)
  const getConvertedPrice = (amount: number | undefined) => {
    if (amount === undefined || amount === null) return null;
    if (userCurrencyCode === "GBP" || !exchangeRate) {
      return amount;
    }
    return amount * exchangeRate;
  };

  const getConvertedSalePrice = (amount: number | undefined) => {
    if (amount === undefined || amount === null) return null;
    if (userCurrencyCode === "GBP" || !exchangeRate) {
      return amount;
    }
    return amount * exchangeRate;
  };

  // Helper function to calculate total price including shipping for the selected quantity
  const getTotalPriceWithShipping = () => {
    const pricing = getCurrentPricing();
    const currentPrice =
      getConvertedSalePrice(pricing.sale_price) ||
      getConvertedPrice(pricing.price) ||
      pricing.sale_price ||
      pricing.price ||
      0;
    const productTotal = currentPrice * quantity;
    const shippingTotal = getConvertedTotalShipping() || 0;
    return productTotal + shippingTotal;
  };

  // Helper function to check if product can be shipped to user's country
  const canShipToUserCountry = () => {
    if (!userAddress?.DeliverTo) return true; // Allow if no address set

    // Check if the user's country exists in any of the shipping rate arrays
    const allShippingCountries = [
      ...shipping_rate_countries.DoorToDoor,
      ...shipping_rate_countries.RestOfAfrica,
    ];

    return allShippingCountries.includes(userAddress.DeliverTo);
  };

  // Helper function to check if a variant option combination is available
  const isOptionAvailable = (optionName: string, optionValue: string) => {
    if (!currentProduct?.variants?.variants) return true;

    // Create a test combination with the proposed option
    const testOptions = {
      ...selectedVariantOptions,
      [optionName]: optionValue,
    };

    // Check if any variant matches this combination
    return currentProduct.variants.variants.some((variant) => {
      return Object.entries(testOptions).every(([key, value]) => {
        const variantValue = getOptionValue(variant.option_values[key]);
        return variantValue === value;
      });
    });
  };

  // Helper function to get count of available variants for an option
  const getAvailableVariantCount = (
    optionName: string,
    optionValue: string,
  ) => {
    if (!currentProduct?.variants?.variants) return 0;

    const testOptions = {
      ...selectedVariantOptions,
      [optionName]: optionValue,
    };

    return currentProduct.variants.variants.filter((variant) => {
      return Object.entries(testOptions).every(([key, value]) => {
        const variantValue = getOptionValue(variant.option_values[key]);
        return variantValue === value;
      });
    }).length;
  };

  // Handle variant option selection
  const handleVariantOptionChange = (
    optionName: string,
    optionValue: string,
  ) => {
    const newOptions = { ...selectedVariantOptions, [optionName]: optionValue };
    setSelectedVariantOptions(newOptions);

    // Find matching variant using currentProduct (already set)
    const matchingVariant = findMatchingVariant(newOptions);
    setSelectedVariant(matchingVariant);

    // Reset selected image when variant changes
    setSelectedImage(0);
  };

  // Get shipping information functions (defined early to avoid hoisting issues)
  const getLocalShipping = () => {
    if (
      !currentProduct?.shipping_rates ||
      currentProduct.shipping_rates.length === 0
    )
      return null;
    if (!userAddress) return null;

    // Check if user is in Accra or Outside Accra
    if (
      userAddress.DeliverTo === "Ghana" &&
      (userAddress.WithinAccra || userAddress.OutsideAccra)
    ) {
      return currentProduct.shipping_rates.find(
        (rate) =>
          (userAddress.WithinAccra &&
            rate.to_zone === "Accra" &&
            rate.to_country === "Ghana") ||
          (userAddress.OutsideAccra &&
            rate.to_zone === "Outside Accra" &&
            rate.to_country === "Ghana"),
      );
    }

    // Check if user is in Lagos or Outside Lagos
    if (
      userAddress.DeliverTo === "Nigeria" &&
      (userAddress.WithinLagos || userAddress.OutsideLagos)
    ) {
      return currentProduct.shipping_rates.find(
        (rate) =>
          (userAddress.WithinLagos &&
            rate.to_zone === "Lagos" &&
            rate.to_country === "Nigeria") ||
          (userAddress.OutsideLagos &&
            rate.to_zone === "Outside Lagos" &&
            rate.to_country === "Nigeria"),
      );
    }

    return null;
  };

  const getInternationalShipping = () => {
    if (
      !currentProduct?.shipping_rates ||
      currentProduct.shipping_rates.length === 0
    )
      return null;

    // Check for "Rest of Africa" rates first
    const restOfAfricaRate = currentProduct.shipping_rates.find(
      (rate) => rate.to_country === "Rest Of Africa",
    );

    if (restOfAfricaRate && userAddress?.DeliverTo) {
      // Check if user's country is in the RestOfAfrica array
      if (
        shipping_rate_countries.RestOfAfrica.includes(userAddress.DeliverTo)
      ) {
        return restOfAfricaRate;
      }
      // If user's country is not in RestOfAfrica, don't return this rate
      return null;
    }

    return null;
  };

  // Convert shipping/duty on-the-fly during render (no effect/state). This ensures currency is always taken
  // from CurrencyProvider and avoids redundant effects.
  const getConvertedLocalShipping = (): number | null => {
    const local = getLocalShipping();
    if (!local || typeof local.base_rate !== "number") return null;
    return exchangeRate ? local.base_rate * exchangeRate : local.base_rate;
  };

  const getConvertedLocalDuty = (): number | null => {
    const local = getLocalShipping();
    if (!local) return null;
    // Duty is charged per item — multiply the per-item duty by the selected quantity
    const dutyPerItem = typeof local.duty === "number" ? local.duty : 0;
    const dutyTotal = dutyPerItem * quantity;
    return exchangeRate ? dutyTotal * exchangeRate : dutyTotal;
  };

  const getConvertedInternationalShipping = (): number | null => {
    const intl = getInternationalShipping();
    if (!intl || typeof intl.base_rate !== "number") return null;
    return exchangeRate ? intl.base_rate * exchangeRate : intl.base_rate;
  };

  const getConvertedTotalShipping = (): number | null => {
    const local = getLocalShipping();
    const intl = getInternationalShipping();

    const localRate =
      local && typeof local.base_rate === "number" ? local.base_rate : 0;
    // multiply per-item duty by quantity for total local duty
    const localDutyPerItem =
      local && typeof local.duty === "number" ? local.duty : 0;
    const localDuty = localDutyPerItem * quantity;
    const intlRate =
      intl && typeof intl.base_rate === "number" ? intl.base_rate : 0;

    const total = localRate + localDuty + intlRate;
    if (total <= 0) return null;
    return exchangeRate ? total * exchangeRate : total;
  };

  // Load user address and currency
  useEffect(() => {
    const loadUserAddress = async () => {
      try {
        const address = await addressService.fetchUserAddress();
        setUserAddress(address);
        // Currency selection is handled by CurrencyProvider; do not set local currency here.
      } catch (error) {
        console.error("Failed to load user address:", error);
      }
    };

    loadUserAddress();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    const fetchProduct = async () => {
      try {
        setLoading(true);
        setError(null);
        const productService = new ProductService();
        const product = await productService.getProduct(params.productId);
        setCurrentProduct(product);

        // Initialize variant selection if product has variants
        if (
          product.variants?.master_options &&
          product.variants.master_options.length > 0
        ) {
          const initialOptions: Record<string, string> = {};

          // Set default selection to first option for each attribute
          product.variants.master_options.forEach((option) => {
            const [optionName, optionValues] = Object.entries(option)[0];
            if (optionValues && optionValues.length > 0) {
              initialOptions[optionName] = optionValues[0];
            }
          });

          // Find and set a matching variant (if variants list exists)
          if (
            product.variants?.variants &&
            product.variants.variants.length > 0
          ) {
            const matchingVariant = product.variants.variants.find(
              (variant) => {
                return Object.entries(initialOptions).every(([key, value]) => {
                  const variantValue = getOptionValue(
                    variant.option_values[key],
                  );
                  return variantValue === value;
                });
              },
            );

            setSelectedVariantOptions(initialOptions);
            setSelectedVariant(matchingVariant || null);
          } else {
            // still set the options even if variants array is missing
            setSelectedVariantOptions(initialOptions);
            setSelectedVariant(null);
          }
        }

        // Price conversion is handled by CurrencyProvider and the dedicated conversion helpers.
      } catch (err) {
        setError("Failed to load product. Please try again later.");
        console.error("Error fetching product:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchProduct();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [params.productId]);

  const refundPolicyMsg = {
    true: "Item is refundable. Contact our support.",
    false: "Item is non refundable. Contact our support for any issues.",
  };

  const formatCurrency = (amount: number, fromCurrency: string) => {
    // Use the shared currency formatter which prefers localized Intl output but
    // substitutes known in-country glyphs (e.g. ₦, ₵) when Intl emits a code.
    // Preserve the original product-page visual by requesting 0 fractional digits.
    try {
      return formatCurrencyWithDigits(amount, fromCurrency, 0);
    } catch {
      // Conservative fallback if the helper fails.
      return formatCurrencyFallback(amount, fromCurrency);
    }
  };

  if (loading) {
    return <MPLoading />;
  }

  if (error || !currentProduct) {
    return (
      <div className="min-h-screen bg-neutral-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 mb-4">
            <AlertCircle size={48} className="mx-auto" />
          </div>
          <p className="text-neutral-600">{error || "Product not found"}</p>
        </div>
      </div>
    );
  }

  // Accordion toggle handler
  const toggleAccordion = (index: number) => {
    setActiveAccordion(activeAccordion === index ? null : index);
  };

  // Discount code copy handler
  const copyDiscountCode = () => {
    const discountCode = (currentProduct.details as ExternalProductDetails)
      .discount_code;
    if (discountCode) {
      navigator.clipboard.writeText(discountCode);
      setDiscountCopied(true);
      setTimeout(() => setDiscountCopied(false), 2000);
    }
  };

  // Handle add to cart
  const handleAddToCart = () => {
    if (authLoading) return;
    if (!isAuthenticated) {
      console.log("user is not authenticated");
      setIsAlertDialogOpen(true);
      return;
    }
    if (currentProduct && !isExternalProduct) {
      // Create cart product without the variants structure, only including selected variant
      const { variants, ...productWithoutVariants } = currentProduct;

      const cartProduct = {
        ...productWithoutVariants,
        selectedVariant: selectedVariant,
        selectedVariantOptions: selectedVariantOptions,
        // Override pricing with current variant pricing if available
        primary_data: {
          ...currentProduct.primary_data,
          ...getCurrentPricing(),
        },
      };

      // Add the specified quantity to cart
      for (let i = 0; i < quantity; i++) {
        addToCart(dispatch, cartProduct);
      }
    }
  };

  // Check if product is external
  const isExternalProduct = currentProduct.type === "external";

  // Get external product details if applicable
  const externalDetails = isExternalProduct
    ? (currentProduct.details as ExternalProductDetails)
    : null;

  // Get internal product details if applicable
  const internalDetails = !isExternalProduct
    ? (currentProduct.details as InternalProductDetails)
    : null;

  // Format currency
  const formatPrice = (price: number) => {
    // Preserve previous visual style (no fraction digits) when showing raw product price.
    try {
      return formatCurrencyWithDigits(
        price,
        currentProduct.currency || "GBP",
        0,
      );
    } catch {
      return formatCurrencyFallback(price, currentProduct.currency || "GBP");
    }
  };

  // Compute shipping for this product using volume/weight packer (pack-together policy).
  // We still read legacy `duty` from `getLocalShipping()` where available.
  const localShipping = getLocalShipping();
  const internationalShipping = getInternationalShipping();
  let productParcelSummaries: Array<{
    items: any[];
    totalVolumeCm3: number;
    totalWeightKg: number;
    tier: any;
    basePriceGbp: number;
  }> = [];
  let productShippingBaseGbp = 0;
  let productShippingError: string | null = null;

  try {
    const shippableItem = {
      id: currentProduct?.id,
      dimensions_cm:
        !isExternalProduct && (internalDetails?.dimensions_cm ?? null)
          ? (internalDetails?.dimensions_cm as any)
          : null,
      weight_kg:
        !isExternalProduct && (internalDetails?.weight_kg ?? null)
          ? (internalDetails?.weight_kg as any)
          : null,
      quantity: quantity,
    };

    const packResult = packItemsIntoParcels([shippableItem]);
    productParcelSummaries = packResult.parcels as any[];
    // Compute country-specific base GBP total using tier gh/ng prices when available.
    productShippingBaseGbp = productParcelSummaries.reduce((sum, parcel) => {
      const tier = parcel.tier;
      let base = parcel.basePriceGbp || 0;
      if (tier) {
        // Prefer country-specific tier prices for Ghana / Nigeria
        if (
          userAddress?.DeliverTo === "Ghana" &&
          typeof (tier as any).ghPriceGbp === "number"
        ) {
          base = (tier as any).ghPriceGbp;
        } else if (
          userAddress?.DeliverTo === "Nigeria" &&
          typeof (tier as any).ngPriceGbp === "number"
        ) {
          base = (tier as any).ngPriceGbp;
        } else if (typeof (tier as any).priceGbp === "number") {
          base = (tier as any).priceGbp;
        }
      }
      return sum + base;
    }, 0);
  } catch (err: any) {
    // packer throws a JS Error for invalid/oversize units — capture message for UI
    productParcelSummaries = [];
    productShippingBaseGbp = 0;
    productShippingError = err?.message || String(err);
  }

  // Legacy local duty (if present) - duty is charged per-item and multiplied by quantity
  const legacyLocalRate = getLocalShipping();
  const legacyDutyPerItem =
    legacyLocalRate && typeof legacyLocalRate.duty === "number"
      ? legacyLocalRate.duty
      : 0;
  const productDutyBaseGbp = legacyDutyPerItem * quantity;

  // Converted totals (respecting CurrencyProvider's exchangeRate)
  const productTotalShippingGbp = productShippingBaseGbp + productDutyBaseGbp;
  const convertedProductTotalShipping = productTotalShippingGbp
    ? exchangeRate
      ? productTotalShippingGbp * exchangeRate
      : productTotalShippingGbp
    : null;

  // FAQ data
  const faqItems = [
    {
      question: "How do I use DermaVerde?",
      answer:
        "Apply a small amount to clean skin morning and evening. Gently massage in circular motions until fully absorbed. For best results, use after cleansing and before sunscreen.",
    },
    {
      question: "Is DermaVerde suitable for sensitive skin?",
      answer:
        "Yes, DermaVerde is formulated for all skin types, including sensitive skin. It's fragrance-free and dermatologically tested to minimize irritation.",
    },
    {
      question: "How long does one bottle last?",
      answer:
        "With regular daily use (morning and evening), one bottle typically lasts 2-3 months.",
    },
    {
      question: "Is DermaVerde cruelty-free?",
      answer:
        "Yes, DermaVerde is 100% vegan and cruelty-free. We never test on animals and don't use any animal-derived ingredients.",
    },
    {
      question: "Can I use DermaVerde with other skincare products?",
      answer:
        "Yes, DermaVerde works well with most skincare regimens. Apply after water-based serums and before heavier creams or oils.",
    },
  ];

  return (
    <div className="min-h-screen bg-neutral-50">
      <Head>
        <title>{currentProduct.title}</title>
        <meta
          name="description"
          content={currentProduct.description.substring(0, 160)}
        />
        <link rel="icon" href="/favicon.ico" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Brand name / Supplier */}
        <div className="mb-6">
          <p className="text-neutral-600 text-sm">
            {isExternalProduct ? (
              <>
                By{" "}
                <span className="font-medium">
                  {externalDetails?.origin_name}
                </span>
              </>
            ) : (
              <>
                Stock:{" "}
                <span className="font-medium">
                  {getCurrentPricing().stock} units
                </span>
                {/* · SKU: <span className="font-medium">{internalDetails?.sku}</span> */}
              </>
            )}
          </p>
        </div>

        <div className="flex flex-col lg:flex-row gap-8">
          {/* Product Image Section */}
          <div className="lg:w-3/5">
            <div className="rounded-lg overflow-hidden mb-4 relative h-[40vh] lg:h-[65vh]">
              <Image
                src={
                  getCurrentImages().length > 0
                    ? getCurrentImages()[selectedImage]?.url
                    : "https://placehold.co/400x300"
                }
                alt={currentProduct.title}
                fill
                sizes="(max-width: 1024px) 100vw, 50vw"
                className="object-contain object-center"
                priority
              />
            </div>

            {/* Image Thumbnails */}
            <div className="flex gap-2 overflow-x-auto pb-2">
              {getCurrentImages().map((img: any, index: number) => (
                <button
                  key={index}
                  className={`w-16 h-16 relative rounded-md overflow-hidden border-2 ${
                    selectedImage === index
                      ? "border-black"
                      : "border-transparent"
                  }`}
                  onClick={() => setSelectedImage(index)}
                >
                  <Image
                    src={img.url}
                    alt={`Product view ${index + 1}`}
                    fill
                    sizes="64px"
                    className="object-contain object-center"
                  />
                </button>
              ))}
            </div>

            {/* Description - Desktop (below images) */}
            <div className="hidden lg:block text-neutral-700 mt-4 mb-6 prose prose-sm max-w-none">
              <ReactMarkdown>{currentProduct.description}</ReactMarkdown>
            </div>
          </div>

          {/* Product Info */}
          <div className="lg:w-2/5 flex flex-col">
            <h1 className="text-3xl sm:text-4xl font-medium text-neutral-900 mb-2">
              {currentProduct.title}
            </h1>
            {/* Price Section */}
            <div className="mb-4">
              {getCurrentPricing().sale_price ? (
                <div className="flex items-center gap-2">
                  <p className="text-xl sm:text-2xl font-medium text-neutral-900">
                    {isLoadingExchangeRate && userCurrencyCode !== "GBP" ? (
                      <Skeleton className="w-24 h-6" />
                    ) : (
                      formatCurrency(
                        getConvertedSalePrice(getCurrentPricing().sale_price) ||
                          getConvertedPrice(getCurrentPricing().price) ||
                          getCurrentPricing().sale_price,
                        userCurrencyCode,
                      )
                    )}
                  </p>
                  {currentProduct.primary_data.discount_percent && (
                    <span className="bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded">
                      {currentProduct.primary_data.discount_percent}% OFF
                    </span>
                  )}
                </div>
              ) : (
                <p className="text-xl sm:text-2xl font-medium text-neutral-900">
                  {isLoadingExchangeRate && userCurrencyCode !== "GBP" ? (
                    <Skeleton className="w-24 h-6" />
                  ) : (
                    formatCurrency(
                      getConvertedPrice(getCurrentPricing().price) ||
                        getCurrentPricing().price,
                      userCurrencyCode,
                    )
                  )}
                </p>
              )}
            </div>

            {/* Refund Status Tag */}
            <div className="mb-4">
              {currentProduct.refundable ? (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-cyan-100 text-cyan-800">
                  <Check size={14} className="mr-1" />
                  Refundable
                </span>
              ) : (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-cyan-100 text-cyan-800">
                  <X size={14} className="mr-1" />
                  Not Refundable
                </span>
              )}
            </div>

            {/* Stock Status (rendered as pills to match refund pill style) */}
            <div className="mb-6">
              {isExternalProduct ? (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium text-blue-600 bg-blue-50">
                  <ExternalLink size={16} className="mr-1" />
                  Available from {externalDetails?.origin_name}
                </span>
              ) : // Check if we have variants but no valid selection
              currentProduct.variants?.variants && !selectedVariant ? (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-amber-50 text-amber-800">
                  <AlertCircle size={16} className="mr-1" />
                  Please select a valid combination
                </span>
              ) : getCurrentPricing().stock > 0 ? (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-700">
                  <Check size={16} className="mr-1" />
                  In Stock - {getCurrentPricing().stock} available
                </span>
              ) : (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-rose-100 text-rose-800">
                  <X size={16} className="mr-1" />
                  Out of Stock
                </span>
              )}
            </div>

            {/* Origin Location */}
            <div className="flex items-center mb-4 text-neutral-600">
              <MapPin size={16} className="mr-1" />
              <span className="text-sm">
                Ships from {currentProduct.origin_location}
              </span>

              {!isExternalProduct && internalDetails && (
                <span className="text-sm ml-2">
                  · Stored in {internalDetails.location}
                </span>
              )}
            </div>
            {/* Quantity Selector - Only show for internal products with valid variant selection */}
            {!isExternalProduct &&
              getCurrentPricing().stock > 0 &&
              (!currentProduct.variants?.variants || selectedVariant) && (
                <div className="mb-6">
                  <label
                    htmlFor="quantity"
                    className="block text-neutral-900 mb-2"
                  >
                    Quantity
                  </label>
                  <div className="flex items-center">
                    <button
                      onClick={() => quantity > 1 && setQuantity(quantity - 1)}
                      className="w-8 h-8 flex items-center justify-center border border-neutral-300 rounded-l-md"
                      aria-label="Decrease quantity"
                    >
                      -
                    </button>
                    <input
                      type="number"
                      id="quantity"
                      name="quantity"
                      value={quantity}
                      onChange={(e) => {
                        const value = parseInt(e.target.value);
                        if (
                          !isNaN(value) &&
                          value > 0 &&
                          value <= getCurrentPricing().stock
                        )
                          setQuantity(value);
                      }}
                      min="1"
                      max={getCurrentPricing().stock}
                      className="w-12 text-center border-y border-neutral-300 h-8"
                    />
                    <button
                      onClick={() =>
                        quantity < getCurrentPricing().stock &&
                        setQuantity(quantity + 1)
                      }
                      className="w-8 h-8 flex items-center justify-center border border-neutral-300 rounded-r-md"
                      aria-label="Increase quantity"
                    >
                      +
                    </button>
                  </div>
                </div>
              )}

            {/* Variant Selection */}
            {currentProduct.variants?.master_options &&
              currentProduct.variants.master_options.length > 0 && (
                <div className="mb-6">
                  {currentProduct.variants.master_options
                    .filter((option) => {
                      const [, optionValues] = Object.entries(option)[0];
                      return !optionValues.includes("N/A");
                    })
                    .map((option, index) => {
                      const [optionName, optionValues] =
                        Object.entries(option)[0];
                      return (
                        <div key={index} className="mb-4">
                          <label className="block text-sm font-medium text-neutral-700 mb-2">
                            {optionName}
                          </label>
                          <div className="flex flex-wrap gap-2">
                            {optionValues.map((value) => {
                              const isAvailable = isOptionAvailable(
                                optionName,
                                value,
                              );
                              const isSelected =
                                selectedVariantOptions[optionName] === value;

                              return (
                                <button
                                  key={value}
                                  onClick={() =>
                                    isAvailable &&
                                    handleVariantOptionChange(optionName, value)
                                  }
                                  disabled={!isAvailable}
                                  className={`px-3 py-2 border rounded-md text-sm transition-colors ${
                                    isSelected
                                      ? "border-black bg-black text-white"
                                      : isAvailable
                                        ? "border-neutral-300 bg-white text-neutral-700 hover:border-neutral-400"
                                        : "border-neutral-200 bg-neutral-100 text-neutral-400 cursor-not-allowed"
                                  }`}
                                >
                                  {value}
                                </button>
                              );
                            })}
                          </div>
                        </div>
                      );
                    })}

                  {/* Show message if no variant is available for current selection */}
                  {!selectedVariant &&
                    Object.keys(selectedVariantOptions).length > 0 && (
                      <div className="mt-3 p-3 bg-amber-50 border border-amber-200 rounded-md">
                        <p className="text-sm text-amber-800">
                          This combination is not available. Please try a
                          different selection.
                        </p>
                      </div>
                    )}
                </div>
              )}

            {/* Discount Code - Only for external products */}
            {isExternalProduct && externalDetails?.discount_code && (
              <div className="mb-6">
                <label className="block text-neutral-900 mb-2">
                  Discount Code
                </label>
                <div className="flex items-center border border-neutral-300 rounded-md overflow-hidden">
                  <div className="flex-1 bg-neutral-50 px-3 py-2 font-medium">
                    {externalDetails.discount_code}
                  </div>
                  <button
                    onClick={copyDiscountCode}
                    className="px-4 py-2 bg-black text-white hover:bg-neutral-800 transition-colors flex items-center"
                  >
                    {discountCopied ? (
                      <Check size={16} className="mr-1" />
                    ) : (
                      <Copy size={16} className="mr-1" />
                    )}
                    {discountCopied ? "Copied!" : "Copy"}
                  </button>
                </div>
                <p className="text-neutral-600 text-sm mt-1">
                  Use this code at {externalDetails.origin_name} for additional
                  savings!
                </p>
              </div>
            )}

            {/* Shipping Information */}
            <div className="bg-neutral-100 rounded-lg p-4 mb-6">
              <h3 className="font-medium mb-2 flex items-center">
                <Truck size={16} className="mr-2" />
                Shipping Information
              </h3>

              {/* Alert when all address flags are NULL */}
              {userAddress &&
                userAddress.WithinAccra === null &&
                userAddress.OutsideAccra === null &&
                userAddress.WithinLagos === null &&
                userAddress.OutsideLagos === null && (
                  <Alert className="border-orange-200 bg-orange-50">
                    <AlertDescription className="text-orange-800">
                      Please update your address details.
                    </AlertDescription>
                  </Alert>
                )}

              {/* Alert when product cannot be shipped to user's country */}
              {userAddress?.DeliverTo && !canShipToUserCountry() && (
                <Alert className="border-red-200 bg-red-50 mb-3">
                  <AlertCircle size={16} className="text-red-600" />
                  <AlertDescription className="text-red-800">
                    This product cannot be shipped to {userAddress.DeliverTo}.
                    Please contact support for more information.
                  </AlertDescription>
                </Alert>
              )}

              <div className="space-y-2 text-sm">
                {productShippingError ? (
                  <Alert className="border-red-200 bg-red-50 mb-3">
                    <AlertCircle size={16} className="text-red-600" />
                    <AlertDescription className="text-red-800">
                      {productShippingError}
                    </AlertDescription>
                  </Alert>
                ) : (
                  <>
                    {productParcelSummaries &&
                    productParcelSummaries.length > 0 ? (
                      <div className="text-sm">
                        <div className="font-medium mb-2">Shipping:</div>
                        {productParcelSummaries.map((parcel, idx) => {
                          let parcelPriceGbp = parcel.basePriceGbp || 0;
                          if (parcel.tier) {
                            if (
                              userAddress?.DeliverTo === "Ghana" &&
                              typeof parcel.tier.ghPriceGbp === "number"
                            ) {
                              parcelPriceGbp = parcel.tier.ghPriceGbp;
                            } else if (
                              userAddress?.DeliverTo === "Nigeria" &&
                              typeof parcel.tier.ngPriceGbp === "number"
                            ) {
                              parcelPriceGbp = parcel.tier.ngPriceGbp;
                            } else if (
                              typeof parcel.tier.priceGbp === "number"
                            ) {
                              parcelPriceGbp = parcel.tier.priceGbp;
                            }
                          }
                          const convertedParcelPrice = exchangeRate
                            ? parcelPriceGbp * exchangeRate
                            : parcelPriceGbp;
                          const vol = parcel.totalVolumeCm3 || 0;
                          const wt = parcel.totalWeightKg || 0;
                          return (
                            <div key={idx} className="mb-2">
                              <div className="flex justify-between">
                                <div>
                                  Parcel {idx + 1} ({parcel.items.length}{" "}
                                  {parcel.items.length !== 1 ? "items" : "item"}
                                  ) :
                                </div>
                                <div className="font-medium">
                                  {isLoadingExchangeRate &&
                                  userCurrencyCode !== "GBP" ? (
                                    <Skeleton className="w-24 h-6" />
                                  ) : (
                                    formatCurrency(
                                      convertedParcelPrice || parcelPriceGbp,
                                      userCurrencyCode,
                                    )
                                  )}
                                </div>
                              </div>
                              <div className="text-xs text-neutral-600 mt-1">
                                {parcel.tier
                                  ? `${parcel.tier.name} • ${Number(wt).toFixed(2)} kg • ${Number(
                                      vol,
                                    ).toFixed(2)} cm³`
                                  : `${Number(wt).toFixed(2)} kg • ${Number(
                                      vol,
                                    ).toFixed(2)} cm³`}
                              </div>
                            </div>
                          );
                        })}
                        <div className="flex justify-between mt-2">
                          <div className="font-medium">Duty:</div>
                          <div className="font-medium">
                            {isLoadingExchangeRate &&
                            userCurrencyCode !== "GBP" ? (
                              <Skeleton className="w-24 h-6" />
                            ) : (
                              formatCurrency(
                                exchangeRate
                                  ? productDutyBaseGbp * exchangeRate
                                  : productDutyBaseGbp,
                                userCurrencyCode,
                              )
                            )}
                          </div>
                        </div>
                        <div className="flex justify-between mt-1 font-bold">
                          <div>Total:</div>
                          <div>
                            {isLoadingExchangeRate &&
                            userCurrencyCode !== "GBP" ? (
                              <Skeleton className="w-24 h-6" />
                            ) : (
                              formatCurrency(
                                convertedProductTotalShipping ||
                                  productTotalShippingGbp ||
                                  0,
                                userCurrencyCode,
                              )
                            )}
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="text-neutral-600">
                        Shipping estimate unavailable
                      </div>
                    )}
                  </>
                )}
              </div>
              {localShipping && (
                <div className="mt-3 flex items-start">
                  <div className="mr-2 text-green-600 mt-0.5">
                    <Check size={16} />
                  </div>
                  <p className="text-sm text-neutral-600">
                    {localShipping
                      ? `Door-to-door delivery available in ${localShipping.to_country} (${localShipping.estimated_delivery_days || 0} days)`
                      : "Shipping details available at checkout"}
                  </p>
                </div>
              )}
            </div>

            {/* Product Dimensions - Only for internal products */}
            {!isExternalProduct && internalDetails?.dimensions_cm && (
              <div className="flex items-center gap-4 mb-6 text-neutral-600 text-sm">
                <Box size={16} />
                <span>
                  {internalDetails.dimensions_cm.length} ×{" "}
                  {internalDetails.dimensions_cm.width} ×{" "}
                  {internalDetails.dimensions_cm.height} cm
                  {internalDetails.weight_kg &&
                    ` (${internalDetails.weight_kg} kg)`}
                </span>
              </div>
            )}

            {/* Call to Action Button */}
            {isExternalProduct ? (
              <a
                href={externalDetails?.origin_url}
                target="_blank"
                rel="noopener noreferrer"
                className="bg-black text-white py-3 sm:py-4 px-6 rounded-full mb-6 hover:bg-neutral-800 transition-colors w-full flex justify-center items-center"
              >
                <ExternalLink size={16} className="mr-2" />
                View on {externalDetails?.origin_name}
              </a>
            ) : // Check if product cannot be shipped to user's country
            userAddress?.DeliverTo && !canShipToUserCountry() ? (
              <button className="bg-red-100 text-red-700 py-3 sm:py-4 px-6 rounded-full mb-6 cursor-not-allowed w-full">
                Cannot Ship to {userAddress.DeliverTo}
              </button>
            ) : // Check if we have variants but no valid selection
            currentProduct.variants?.variants && !selectedVariant ? (
              <button className="bg-amber-100 text-amber-700 py-3 sm:py-4 px-6 rounded-full mb-6 cursor-not-allowed w-full">
                Please Select Valid Options
              </button>
            ) : getCurrentPricing().stock > 0 ? (
              <button
                onClick={handleAddToCart}
                className="bg-black text-white py-3 sm:py-4 px-6 rounded-full mb-6 hover:bg-neutral-800 transition-colors w-full"
              >
                Add to Cart {quantity > 1 ? `(${quantity})` : ""} (
                {formatCurrency(getTotalPriceWithShipping(), userCurrencyCode)})
              </button>
            ) : (
              <button className="bg-neutral-300 text-neutral-500 py-3 sm:py-4 px-6 rounded-full mb-6 cursor-not-allowed w-full">
                Out of Stock
              </button>
            )}

            {/* Why buy from us link */}
            <div className="text-center mb-6">
              <a
                href="/blog/why-buy-from-us"
                className="text-blue-600 hover:text-blue-700 text-sm font-medium underline underline-offset-4 transition-colors inline-flex items-center justify-center"
              >
                <MessageCircleQuestion size={14} className="mr-2" />
                Why buy from us?
              </a>
            </div>

            <div className="block lg:hidden text-neutral-700 mt-4 mb-6 prose prose-sm max-w-none">
              <ReactMarkdown>{currentProduct.description}</ReactMarkdown>
            </div>

            {/* Affiliate Notice */}
            {isExternalProduct && externalDetails?.is_affiliate && (
              <div className="flex items-center justify-center mb-6 text-sm text-neutral-600">
                <AlertCircle size={14} className="mr-1" />
                This link contains an affiliate code
              </div>
            )}

            <div className="border-t border-neutral-200">
              <button
                className="flex justify-between items-center w-full py-4 text-left"
                onClick={() => toggleAccordion(1)}
              >
                <span className="text-lg font-medium">Shipping Info</span>
                <ChevronDown
                  className={`transition-transform ${activeAccordion === 1 ? "rotate-180" : ""}`}
                  size={20}
                />
              </button>
              {activeAccordion === 1 && (
                <div className="pb-4 text-neutral-700">
                  <h4 className="font-medium mb-2">Shipping Policy</h4>
                  <p className="mb-2">
                    {currentProduct.origin_location === "UK"
                      ? "International shipping from the UK takes 7-14 business days. Local delivery takes 1-3 business days after arrival in your country."
                      : "Local delivery within Ghana takes 1-3 business days. Shipping to other locations may take 5-14 business days."}
                  </p>
                  <h4 className="font-medium mb-2 mt-4">Return Policy</h4>
                  {currentProduct.refundable && (
                    <p>
                      {currentProduct.refund_policy.length != 0
                        ? currentProduct.refund_policy
                        : refundPolicyMsg.true}
                    </p>
                  )}
                  {!currentProduct.refundable && (
                    <p>
                      {currentProduct.refund_policy.length != 0
                        ? currentProduct.refund_policy
                        : refundPolicyMsg.false}
                    </p>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* External Product Disclaimer */}
        {isExternalProduct && (
          <div className="bg-neutral-50 border border-neutral-200 rounded-lg p-4 mb-8 mt-8">
            <div className="flex items-start">
              <AlertCircle
                size={20}
                className="text-amber-500 mt-0.5 mr-3 flex-shrink-0"
              />
              <div>
                <h4 className="font-medium text-neutral-900 mb-1">
                  Important Notice
                </h4>
                <p className="text-sm text-neutral-700">
                  MailPallet is not affiliated with{" "}
                  {externalDetails?.origin_name} and does not manufacture, sell,
                  or distribute this product. We provide this listing as a
                  service to our users. Any purchases made through external
                  links are subject to the terms, conditions, and privacy
                  policies of the original retailer. MailPallet makes no
                  warranties regarding products purchased from external sites
                  and assumes no liability for such transactions.
                </p>
              </div>
            </div>
          </div>
        )}
      </main>

      <AlertDialog
        open={isAlertDialogOpen}
        onOpenChange={(v) => setIsAlertDialogOpen(v)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-blue-500" />
              Sign In Required
            </AlertDialogTitle>
            <AlertDialogDescription>
              You need to be signed in to add items to your cart. Please sign in
              to your account or create a new one to continue shopping.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className="flex-col sm:flex-row gap-2">
            <AlertDialogCancel className="w-full sm:w-auto">
              Continue Browsing
            </AlertDialogCancel>
            <AlertDialogAction
              className="w-full sm:w-auto bg-blue-600 hover:bg-blue-700"
              onClick={() => {
                setIsAlertDialogOpen(false);
                window.location.href = "/login";
              }}
            >
              Register/Login
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
